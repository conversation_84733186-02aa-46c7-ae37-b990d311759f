{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError fetching SMS status: this.client.messages is not a function\u001b[39m","stack":"TypeError: this.client.messages is not a function\n    at TwilioService.messages [as getSMSStatus] (/home/<USER>/Exercise/NODEJS/Twilio/src/services/twilioService.js:30:41)\n    at Object.getSMSStatus (/home/<USER>/Exercise/NODEJS/Twilio/tests/unit/services/twilioService.test.js:37:42)\n    at Promise.then.completed (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/jest-circus/build/run.js:121:9)\n    at _runTestsForDescribeBlock (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/jest-runner/build/testWorker.js:106:12)","timestamp":"2025-08-31 14:02:25:225"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mTwilio call error: Either url or twiml must be provided\u001b[39m","stack":"Error: Either url or twiml must be provided\n    at TwilioService.makeCall (/home/<USER>/Exercise/NODEJS/Twilio/src/services/twilioService.js:61:15)\n    at makeCall (/home/<USER>/Exercise/NODEJS/Twilio/src/controllers/twilioController.js:47:42)\n    at Layer.handle [as handle_request] (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/route.js:149:13)\n    at next (/home/<USER>/Exercise/NODEJS/Twilio/src/middleware/validation.js:142:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/layer.js:95:5)\n    at /home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:175:3)\n    at router (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:328:13)\n    at /home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:175:3)\n    at router (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:328:13)\n    at /home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:280:10)\n    at serveStatic (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/serve-static/index.js:75:16)\n    at Layer.handle [as handle_request] (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:328:13)\n    at /home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/body-parser/lib/types/urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:328:13)\n    at /home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:280:10)\n    at /home/<USER>/Exercise/NODEJS/Twilio/node_modules/body-parser/lib/read.js:137:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/raw-body/index.js:238:16)\n    at done (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/raw-body/index.js:227:7)\n    at IncomingMessage.onEnd (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/raw-body/index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-08-31 14:02:26:226"}
{"isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError making call: Failed to make call: Either url or twiml must be provided\u001b[39m","service":"twilio","stack":"Error: Failed to make call: Either url or twiml must be provided\n    at TwilioService.makeCall (/home/<USER>/Exercise/NODEJS/Twilio/src/services/twilioService.js:70:13)\n    at makeCall (/home/<USER>/Exercise/NODEJS/Twilio/src/controllers/twilioController.js:47:42)\n    at Layer.handle [as handle_request] (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/route.js:149:13)\n    at next (/home/<USER>/Exercise/NODEJS/Twilio/src/middleware/validation.js:142:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/layer.js:95:5)\n    at /home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:175:3)\n    at router (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:328:13)\n    at /home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:175:3)\n    at router (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:328:13)\n    at /home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:280:10)\n    at serveStatic (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/serve-static/index.js:75:16)\n    at Layer.handle [as handle_request] (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:328:13)\n    at /home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/body-parser/lib/types/urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:328:13)\n    at /home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:280:10)\n    at /home/<USER>/Exercise/NODEJS/Twilio/node_modules/body-parser/lib/read.js:137:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/raw-body/index.js:238:16)\n    at done (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/raw-body/index.js:227:7)\n    at IncomingMessage.onEnd (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/raw-body/index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","statusCode":500,"timestamp":"2025-08-31 14:02:26:226"}
{"ip":"::ffff:127.0.0.1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError Failed to make call: Either url or twiml must be provided\u001b[39m","method":"POST","stack":"Error: Failed to make call: Either url or twiml must be provided\n    at TwilioService.makeCall (/home/<USER>/Exercise/NODEJS/Twilio/src/services/twilioService.js:70:13)\n    at makeCall (/home/<USER>/Exercise/NODEJS/Twilio/src/controllers/twilioController.js:47:42)\n    at Layer.handle [as handle_request] (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/route.js:149:13)\n    at next (/home/<USER>/Exercise/NODEJS/Twilio/src/middleware/validation.js:142:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/layer.js:95:5)\n    at /home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:175:3)\n    at router (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:328:13)\n    at /home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:175:3)\n    at router (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:328:13)\n    at /home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:280:10)\n    at serveStatic (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/serve-static/index.js:75:16)\n    at Layer.handle [as handle_request] (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:328:13)\n    at /home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/body-parser/lib/types/urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:328:13)\n    at /home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/express/lib/router/index.js:280:10)\n    at /home/<USER>/Exercise/NODEJS/Twilio/node_modules/body-parser/lib/read.js:137:5\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/raw-body/index.js:238:16)\n    at done (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/raw-body/index.js:227:7)\n    at IncomingMessage.onEnd (/home/<USER>/Exercise/NODEJS/Twilio/node_modules/raw-body/index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-08-31 14:02:26:226","url":"/api/twilio/voice/call"}
